# Zoho Chat Widget Troubleshooting Guide

## Issue: "No articles found" in Zoho Chat Widget

### Problem Description
The Zoho SalesIQ chat widget displays "No articles found" even though there are 2 articles available in the knowledge base.

### Root Cause Analysis
This issue typically occurs due to one of the following reasons:

1. **Knowledge Base Configuration Issues**
2. **Widget Configuration Problems**
3. **User Role/Permission Issues**
4. **API Integration Problems**

### Debugging Steps

#### 1. Check Browser Console
Open your browser's Developer Tools (F12) and check the Console tab for any error messages related to Zoho SalesIQ.

Look for messages starting with `ZohoChatWidget:` - these are debug logs added to help identify the issue.

#### 2. Verify Widget Loading
Check if you see these console messages:
- `ZohoChatWidget: Loading widget for role: [role] with config: [config]`
- `ZohoChatWidget: Widget ready, setting user info`
- `ZohoChatWidget: Script loaded successfully`

#### 3. Check Knowledge Base Availability
Look for this console message:
- `ZohoChatWidget: Knowledge base is available` ✅ Good
- `ZohoChatWidget: Knowledge base not available` ⚠️ Problem identified

### Solutions

#### Solution 1: Verify Zoho SalesIQ Dashboard Configuration

1. **Login to Zoho SalesIQ Dashboard**
   - Go to https://salesiq.zoho.com/
   - Login with your account credentials

2. **Check Knowledge Base Setup**
   - Navigate to `Settings` → `Knowledge Base`
   - Verify that articles are published and not in draft mode
   - Ensure articles are assigned to the correct categories
   - Check if articles have proper tags/categories

3. **Verify Widget Configuration**
   - Go to `Settings` → `Brands` → `[Your Brand]` → `Widget`
   - Ensure Knowledge Base is enabled in widget settings
   - Check if the correct knowledge base is linked to the widget

#### Solution 2: Check Widget Code Configuration

Verify that you're using the correct widget codes in `src/components/ZohoChatWidget.js`:

```javascript
const WIDGETS = {
  Admin: {
    widgetcode: 'siqb95d6ef899c13858d7f5f37fa6171acbe5201291a5ec9676108f4bb3fd896076',
    scriptUrl: 'https://salesiq.zohopublic.in/widget?wc=siqb95d6ef899c13858d7f5f37fa6171acbe5201291a5ec9676108f4bb3fd896076',
  },
  User: {
    widgetcode: 'siqb95d6ef899c13858d7f5f37fa6171acb13781a53e1b74032df8007af1fa7f9b1',
    scriptUrl: 'https://salesiq.zohopublic.in/widget?wc=siqb95d6ef899c13858d7f5f37fa6171acb13781a53e1b74032df8007af1fa7f9b1',
  },
};
```

#### Solution 3: Test with Different User Roles

1. Test the widget with both Admin and User roles
2. Check if the issue persists across different roles
3. Verify role mapping is working correctly

#### Solution 4: Network and CORS Issues

1. **Check Network Tab**
   - Open Developer Tools → Network tab
   - Look for failed requests to `salesiq.zohopublic.in`
   - Check for CORS errors

2. **Verify Domain Whitelist**
   - In Zoho SalesIQ Dashboard, go to `Settings` → `Brands` → `[Your Brand]` → `Widget`
   - Ensure your domain is added to the allowed domains list

### Advanced Debugging

#### Enable Detailed Logging

Add this code to your browser console to enable more detailed Zoho logging:

```javascript
// Enable Zoho SalesIQ debug mode
window.$zoho = window.$zoho || {};
window.$zoho.salesiq = window.$zoho.salesiq || {};
window.$zoho.salesiq.ready = function() {
  console.log('Zoho SalesIQ Ready');
  console.log('Available APIs:', Object.keys(window.$zoho.salesiq));
  
  if (window.$zoho.salesiq.knowledgebase) {
    console.log('Knowledge Base APIs:', Object.keys(window.$zoho.salesiq.knowledgebase));
  }
};
```

#### Test Widget Independently

Create a simple HTML file to test the widget outside of your React app:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Zoho Widget Test</title>
</head>
<body>
    <h1>Testing Zoho Widget</h1>
    
    <script>
        window.$zoho = window.$zoho || {};
        window.$zoho.salesiq = {
            widgetcode: "YOUR_WIDGET_CODE_HERE",
            values: {},
            ready: function() {
                console.log("Widget loaded successfully");
                window.$zoho.salesiq.visitor.name("Test User");
                window.$zoho.salesiq.visitor.info({
                    "Role": "Admin",
                    "Name": "Test User",
                    "Department": "Test Department"
                });
            }
        };
    </script>
    <script src="https://salesiq.zohopublic.in/widget?wc=YOUR_WIDGET_CODE_HERE"></script>
</body>
</html>
```

### Quick Fixes to Try

1. **Clear Browser Cache and Cookies**
   - Clear all cookies for your domain
   - Hard refresh the page (Ctrl+F5)

2. **Test in Incognito Mode**
   - Open your app in an incognito/private browsing window
   - This eliminates cache and cookie issues

3. **Verify User Authentication**
   - Ensure the user is properly logged in
   - Check that `config` and `admin_data` are populated in Redux state

### Contact Support

If the issue persists after trying all solutions:

1. **Zoho SalesIQ Support**
   - Contact Zoho SalesIQ support with your widget code
   - Provide screenshots of the issue and console logs

2. **Check Zoho Status Page**
   - Visit https://status.zoho.com/ to check for service outages

### Prevention

1. **Regular Testing**
   - Test the widget functionality after any updates
   - Monitor console logs for warnings

2. **Documentation**
   - Keep widget codes and configuration documented
   - Maintain a test checklist for widget functionality
